# CPP (Component Preview Protocol)

## Introduction

The Component Preview Protocol (CPP) is an open protocol designed to enable different tools to work with UI components and previews across various platforms and technologies. Similar to Storybook's [Component Story Format (CSF)](https://storybook.js.org/docs/8/api/csf), CPP provides a standardized way to define, discover, and interact with UI component previews. However, unlike CSF which is JavaScript-specific, CPP is language-agnostic and operates cross-process using JSON-RPC, similar to the Model Context Protocol (MCP).

CPP enables:
- **Cross-platform component discovery**: Find UI components and their previews across different platforms (.NET MAUI, WPF, etc.)
- **Tool interoperability**: Allow different development tools to work with the same component definitions
- **Language independence**: Support components written in any language that can implement the protocol
- **Remote communication**: Enable tools to communicate with applications running in different processes or even on different machines

## Protocol Overview

CPP uses JSON-RPC 2.0 over TCP for communication between tools (like HotPreview DevTools) and applications. The protocol defines two main service interfaces:

1. **IPreviewAppService**: Implemented by applications to expose their components and previews
2. **IPreviewAppControllerService**: Implemented by tools to receive notifications from applications

## JSON-RPC Schema

### IPreviewAppService Interface

The main service interface that applications must implement to expose their UI components and previews.

#### Methods

##### GetUIComponentsAsync
Retrieves all available UI components and their associated previews.

**Request:**
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "GetUIComponentsAsync",
  "params": {}
}
```

**Response:**
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": [
    {
      "Name": "MyApp.Views.ProductCard",
      "UIComponentKindInfo": "control",
      "DisplayName": "Product Card",
      "Previews": [
        {
          "PreviewType": "staticMethod",
          "Name": "DefaultPreview",
          "DisplayName": "Default",
          "IsAutoGenerated": false
        }
      ]
    }
  ]
}
```

##### GetUIComponentPreviewsAsync
Retrieves preview names for a specific UI component.

**Request:**
```json
{
  "jsonrpc": "2.0",
  "id": 2,
  "method": "GetUIComponentPreviewsAsync",
  "params": {
    "componentName": "MyApp.Views.ProductCard"
  }
}
```

**Response:**
```json
{
  "jsonrpc": "2.0",
  "id": 2,
  "result": ["DefaultPreview", "WithLongTitle", "OutOfStock"]
}
```

##### NavigateToPreviewAsync
Navigates the application to display a specific preview.

**Request:**
```json
{
  "jsonrpc": "2.0",
  "id": 3,
  "method": "NavigateToPreviewAsync",
  "params": {
    "componentName": "MyApp.Views.ProductCard",
    "previewName": "DefaultPreview"
  }
}
```

**Response:**
```json
{
  "jsonrpc": "2.0",
  "id": 3,
  "result": null
}
```

##### GetPreviewSnapshotAsync
Captures a visual snapshot of a specific preview.

**Request:**
```json
{
  "jsonrpc": "2.0",
  "id": 4,
  "method": "GetPreviewSnapshotAsync",
  "params": {
    "uiComponentName": "MyApp.Views.ProductCard",
    "previewName": "DefaultPreview"
  }
}
```

**Response:**
```json
{
  "jsonrpc": "2.0",
  "id": 4,
  "result": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
}
```

##### GetCommandsAsync
Retrieves all available preview commands.

**Request:**
```json
{
  "jsonrpc": "2.0",
  "id": 5,
  "method": "GetCommandsAsync",
  "params": {}
}
```

**Response:**
```json
{
  "jsonrpc": "2.0",
  "id": 5,
  "result": [
    {
      "Name": "MyApp.Commands.RefreshData",
      "DisplayName": "Refresh Data"
    }
  ]
}
```

##### InvokeCommandAsync
Executes a preview command.

**Request:**
```json
{
  "jsonrpc": "2.0",
  "id": 6,
  "method": "InvokeCommandAsync",
  "params": {
    "commandName": "MyApp.Commands.RefreshData"
  }
}
```

**Response:**
```json
{
  "jsonrpc": "2.0",
  "id": 6,
  "result": null
}
```

### IPreviewAppControllerService Interface

The controller service interface that tools implement to receive notifications from applications.

#### Methods

##### RegisterAppAsync
Registers an application with the tool, providing project and platform information.

**Request:**
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "RegisterAppAsync",
  "params": {
    "projectPath": "/path/to/project",
    "platformName": "MAUI"
  }
}
```

##### NotifyPreviewsChangedAsync
Notifies the tool that the available previews have changed (e.g., due to code changes).

**Request:**
```json
{
  "jsonrpc": "2.0",
  "id": 2,
  "method": "NotifyPreviewsChangedAsync",
  "params": {}
}
```

## Data Types

### UIComponentInfo
Represents information about a UI component and its previews.

```typescript
interface UIComponentInfo {
  Name: string;                    // Fully qualified component name
  UIComponentKindInfo: string;     // "page", "control", or "unknown"
  DisplayName?: string;            // Optional display name override
  Previews: PreviewInfo[];         // Array of available previews
}
```

### PreviewInfo
Represents information about a specific preview.

```typescript
interface PreviewInfo {
  PreviewType: string;       // "class" or "staticMethod"
  Name: string;              // Preview identifier
  DisplayName?: string;      // Optional display name override
  IsAutoGenerated: boolean;  // Whether the preview was auto-generated
}
```

### PreviewCommandInfo
Represents information about an executable command.

```typescript
interface PreviewCommandInfo {
  Name: string;              // Fully qualified command name
  DisplayName?: string;      // Optional display name override
}
```

## Implementation Notes

- All string parameters should use fully qualified names for components and commands to ensure uniqueness
- Binary data (like snapshots) should be base64-encoded
- The protocol supports both auto-generated and manually defined previews
- Component kinds help tools categorize and display components appropriately
- Display names provide human-readable alternatives to technical names

## Connection Establishment

1. **Application Startup**: The application connects to the tool via TCP
2. **Registration**: Application calls `RegisterAppAsync` to identify itself
3. **Discovery**: Tool calls `GetUIComponentsAsync` to discover available components
4. **Interaction**: Tool can navigate to previews, capture snapshots, and execute commands
5. **Change Notification**: Application calls `NotifyPreviewsChangedAsync` when previews change
