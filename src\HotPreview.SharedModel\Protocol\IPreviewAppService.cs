using System.Threading.Tasks;
using StreamJsonRpc;

namespace HotPreview.SharedModel.Protocol;

public interface IPreviewAppService
{
    [JsonRpcMethod("uiComponents/list")]
    public Task<UIComponentInfo[]> GetUIComponentsAsync();

    [JsonRpcMethod("previews/listForUIComponent")]
    public Task<string[]> GetUIComponentPreviewsAsync(string componentName);

    [JsonRpcMethod("previews/navigate")]
    public Task NavigateToPreviewAsync(string componentName, string previewName);

    [JsonRpcMethod("previews/snapshot")]
    public Task<byte[]> GetPreviewSnapshotAsync(string uiComponentName, string previewName);

    [JsonRpcMethod("commands/list")]
    public Task<PreviewCommandInfo[]> GetCommandsAsync();

    [JsonRpcMethod("commands/invoke")]
    public Task InvokeCommandAsync(string commandName);
}
