<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Hot Preview Documentation | Hot Preview Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Hot Preview Documentation | Hot Preview Documentation ">
      
      
      <link rel="icon" href="favicon.ico">
      <link rel="stylesheet" href="public/docfx.min.css">
      <link rel="stylesheet" href="public/main.css">
      <meta name="docfx:navrel" content="toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="">
      
      
      <meta name="docfx:docurl" content="https://github.com/BretJohnson/hot-preview/blob/main/docs/index.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="landing" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="index.html">
            <img id="logo" class="svg" src="logo.svg" alt="Hot Preview">
            Hot Preview
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">

      <div class="content">
        <div class="actionbar">

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="hot-preview-documentation">Hot Preview Documentation</h1>

<p>Hot Preview lets you easily work on pages and controls in your app in isolation, making UI development and testing faster and more efficient for both humans and AI agents.</p>
<p>Previews are similar to stories in <a href="https://storybook.js.org/">Storybook</a> for JavaScript and Previews in <a href="https://developer.apple.com/documentation/xcode/previewing-your-apps-interface-in-xcode">SwiftUI/Xcode</a> and <a href="https://developer.android.com/develop/ui/compose/tooling/previews">Jetpack Compose/Android Studio</a> — but for .NET UI.</p>
<h2 id="quick-start">Quick Start</h2>
<p>Get started with HotPreview in minutes:</p>
<ol>
<li><p><strong>Install Hot Preview DevTools:</strong></p>
<pre><code class="lang-bash">dotnet tool install -g HotPreview.DevTools
</code></pre>
</li>
<li><p><strong>Add package reference to your app:</strong></p>
<pre><code class="lang-xml">&lt;PackageReference Condition=&quot;$(Configuration) == 'Debug'&quot; Include=&quot;HotPreview.App.Maui&quot; Version=&quot;...&quot; /&gt;
</code></pre>
</li>
<li><p><strong>Build and run your app in Debug mode</strong></p>
</li>
</ol>
<h2 id="features">Features</h2>
<ul>
<li>🚀 <strong>Streamlined Navigation</strong> - Jump directly to specific UI pages without complex navigation</li>
<li>🔄 <strong>Multi-state Testing</strong> - Visualize components with different data inputs and states</li>
<li>📱 <strong>Cross-platform Preview</strong> - View UI on multiple platforms simultaneously</li>
<li>🤖 <strong>AI Integration</strong> - Built-in MCP server for agentic AI workflows <em>(Coming Soon)</em></li>
</ul>
<p><a href="docs/getting-started.html">Get Started →</a> | <a href="api/HotPreview.html">API Reference →</a></p>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/BretJohnson/hot-preview/blob/main/docs/index.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
