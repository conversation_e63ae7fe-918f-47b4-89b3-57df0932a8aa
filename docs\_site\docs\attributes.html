<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Hot Preview Attributes Reference | Hot Preview Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Hot Preview Attributes Reference | Hot Preview Documentation ">
      
      
      <link rel="icon" href="../favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/BretJohnson/hot-preview/blob/main/docs/docs/attributes.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../logo.svg" alt="Hot Preview">
            Hot Preview
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="hot-preview-attributes-reference">Hot Preview Attributes Reference</h1>

<p>Hot  Preview provides several attributes to control how your UI components are discovered, displayed, and organized in the DevTools interface. This reference covers all available attributes and their usage.</p>
<h2 id="core-attributes">Core Attributes</h2>
<h3 id="previewattribute">PreviewAttribute</h3>
<p>The <code>[Preview]</code> attribute is the primary way to define custom previews for your UI components.</p>
<p><strong>Target:</strong> Methods and Classes
<strong>Namespace:</strong> <code>HotPreview</code></p>
<h4 id="overview">Overview</h4>
<p>The <code>PreviewAttribute</code> comes in two forms:</p>
<ul>
<li><strong>Non-generic</strong>: <code>PreviewAttribute</code> - Automatically infers UI component type</li>
<li><strong>Generic</strong>: <code>PreviewAttribute&lt;TUIComponent&gt;</code> - Explicitly specifies UI component type</li>
</ul>
<h4 id="parameters">Parameters</h4>
<table>
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>displayName</code></td>
<td><code>string?</code></td>
<td>Optional display name override for the preview, determining how it appears in navigation UI. If not specified, uses method/class name converted to start case (e.g., &quot;MyPreview&quot; becomes &quot;My Preview&quot;)</td>
</tr>
</tbody>
</table>
<h4 id="type-inference">Type Inference</h4>
<ul>
<li><strong>Method return type</strong>: UI component type is inferred from the method's return type</li>
<li><strong>Void methods</strong>: UI component type is inferred from the containing class</li>
<li><strong>Explicit specification</strong>: Use <code>PreviewAttribute&lt;TUIComponent&gt;</code> when you need to specify a different UI component type</li>
</ul>
<h4 id="constructors">Constructors</h4>
<pre><code class="lang-csharp">[Preview]                             // Typical preview - default display name, automatic UI component type inference
[Preview(&quot;Display Name&quot;)]             // Overridden display name, with automatic UI component type inference
[Preview&lt;MyComponent&gt;]                // Explicitly specified UI component type
[Preview&lt;MyComponent&gt;(&quot;Display Name&quot;)]  // Overridden display name, with explicit UI component type
</code></pre>
<h4 id="usage-examples">Usage Examples</h4>
<pre><code class="lang-csharp">#if PREVIEWS
    // Basic preview with automatic type inference
    [Preview]
    public static CardView Preview() =&gt; new(PreviewData.GetCards(3));

    // Named previews with automatic type inference
    [Preview(&quot;0 cards&quot;)]
    public static CardView NoCards() =&gt; new(PreviewData.GetCards(0));

    [Preview(&quot;1 Card&quot;)]
    public static CardView SingleCard() =&gt; new(PreviewData.GetCards(1));

    [Preview(&quot;6 Cards&quot;)]
    public static CardView MultipleCards() =&gt; new(PreviewData.GetCards(6));

    // void method - UI component type inferred from containing class
    public static void AppNeedsUpdateState()
    {
    }

    // Explicit UI component type specification using generic attribute
    [Preview&lt;ProductView&gt;]
    public static CustomProductLayoutView CustomProductLayout() =&gt; new(PreviewData.GetProduct());
#endif
</code></pre>
<h3 id="uicomponentattribute">UIComponentAttribute</h3>
<p>The <code>[UIComponent]</code> attribute allows you to explicitly mark classes as UI components and provide custom display names.</p>
<p><strong>Target:</strong> Classes
<strong>Namespace:</strong> <code>HotPreview</code></p>
<h4 id="overview-1">Overview</h4>
<p>Normally UI components don't need to be defined explicitly (defining a preview is sufficient), but this attribute can be used to define a display name for the component.</p>
<h4 id="parameters-1">Parameters</h4>
<table>
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>displayName</code></td>
<td><code>string?</code></td>
<td>Optional display name override for the UI component. If not specified, the class name is used (without namespace)</td>
</tr>
</tbody>
</table>
<h4 id="usage-examples-1">Usage Examples</h4>
<pre><code class="lang-csharp">// Explicit display name for UI component
[UIComponent(&quot;Shopping Cart&quot;)]
public partial class CartView : ContentView
{
    // Component implementation
}
</code></pre>
<h3 id="previewcommandattribute">PreviewCommandAttribute</h3>
<p>The <code>[PreviewCommand]</code> attribute defines commands that can be executed from the DevTools interface.</p>
<p><strong>Target:</strong> Methods
<strong>Namespace:</strong> <code>HotPreview</code></p>
<h4 id="parameters-2">Parameters</h4>
<table>
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>displayName</code></td>
<td><code>string?</code></td>
<td>Optional display name override for the command, determining how it appears in navigation UI. If not specified, the method name is used</td>
</tr>
</tbody>
</table>
<h4 id="usage-examples-2">Usage Examples</h4>
<pre><code class="lang-csharp">#if PREVIEWS
    [PreviewCommand]
    public static void ClearCache()
    {
        // Clear application cache
    }

    [PreviewCommand(&quot;Reset Application State&quot;]
    public static void ResetAppState()
    {
        // Reset global state for testing
    }
#endif
</code></pre>
<h2 id="configuration-attributes">Configuration Attributes</h2>
<h3 id="autogeneratepreviewattribute">AutoGeneratePreviewAttribute</h3>
<p>Controls whether auto-generated previews should be created for a UI component.</p>
<p><strong>Target:</strong> Classes
<strong>Namespace:</strong> <code>HotPreview</code></p>
<h4 id="overview-2">Overview</h4>
<p>When present on a class and the <code>autoGenerate</code> property is <code>false</code>, auto-generation is disabled. This attribute provides explicit control over the auto-generation behavior for UI components.
It's appropriate to use if the auto-generated preview doesn't work properly, so you don't want to see it in the UI, and you don't want to define a explicit preview for the component.</p>
<h4 id="parameters-3">Parameters</h4>
<table>
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>autoGenerate</code></td>
<td><code>bool</code></td>
<td>Controls whether auto-generated previews should be created for this component. When set to <code>false</code>, auto-generation is disabled</td>
</tr>
</tbody>
</table>
<h4 id="usage-examples-3">Usage Examples</h4>
<pre><code class="lang-csharp">// Disable auto-generated previews for this component
[AutoGeneratePreview(false)]
public partial class ComplexView : ContentView
{
    // This component won't get an auto-generated preview, even if it has a parameterless constructor
}
</code></pre>
<h2 id="assembly-level-attributes">Assembly-Level Attributes</h2>
<h3 id="uicomponentcategoryattribute">UIComponentCategoryAttribute</h3>
<p>Defines categories for organizing UI components in the DevTools interface.</p>
<p><strong>Target:</strong> Assembly
<strong>Namespace:</strong> <code>HotPreview</code></p>
<h4 id="overview-3">Overview</h4>
<p>Categories are used for display purposes only. If no category is specified for a component, the category name defaults to &quot;Pages&quot; or &quot;Controls&quot;, depending on whether the UI component is a page or not. This attribute can be specified multiple times for a single category, in which case the UI components are combined together.</p>
<h4 id="parameters-4">Parameters</h4>
<table>
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>name</code></td>
<td><code>string</code></td>
<td>The name of the category</td>
</tr>
<tr>
<td><code>uiComponents</code></td>
<td><code>Type[]</code></td>
<td>The UI components that belong to this category</td>
</tr>
</tbody>
</table>
<h4 id="usage-examples-4">Usage Examples</h4>
<pre><code class="lang-csharp">// In AssemblyInfo.cs or any source file
using HotPreview;

[assembly: UIComponentCategory(&quot;Navigation&quot;, typeof(HeaderView), typeof(FooterView), typeof(TabBar))]
[assembly: UIComponentCategory(&quot;Cards&quot;, typeof(ProductCard), typeof(CategoryCard), typeof(InfoCard))]
[assembly: UIComponentCategory(&quot;Forms&quot;, typeof(LoginForm), typeof(RegisterForm), typeof(ContactForm))]

// Multiple attributes for the same category combine the components
[assembly: UIComponentCategory(&quot;Data Display&quot;, typeof(ProductCard), typeof(UserProfile))]
[assembly: UIComponentCategory(&quot;Data Display&quot;, typeof(StatisticsView), typeof(ChartView))]
</code></pre>
<h3 id="controluicomponentbasetypeattribute">ControlUIComponentBaseTypeAttribute</h3>
<p>Specifies the base type for control UI components on a specific platform.</p>
<p><strong>Target:</strong> Assembly
<strong>Namespace:</strong> <code>HotPreview</code></p>
<h4 id="parameters-5">Parameters</h4>
<table>
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>platform</code></td>
<td><code>string</code></td>
<td>Platform identifier (e.g., &quot;MAUI&quot;, &quot;WPF&quot;)</td>
</tr>
<tr>
<td><code>baseType</code></td>
<td><code>string</code></td>
<td>Fully qualified base type name</td>
</tr>
</tbody>
</table>
<h4 id="usage-examples-5">Usage Examples</h4>
<pre><code class="lang-csharp">// In AssemblyInfo.cs
[assembly: ControlUIComponentBaseType(&quot;MAUI&quot;, &quot;Microsoft.Maui.Controls.View&quot;)]
[assembly: ControlUIComponentBaseType(&quot;WPF&quot;, &quot;System.Windows.Controls.Control&quot;)]
</code></pre>
<h3 id="pageuicomponentbasetypeattribute">PageUIComponentBaseTypeAttribute</h3>
<p>Specifies the base type for page UI components on a specific platform.</p>
<p><strong>Target:</strong> Assembly
<strong>Namespace:</strong> <code>HotPreview</code></p>
<h4 id="parameters-6">Parameters</h4>
<table>
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>platform</code></td>
<td><code>string</code></td>
<td>Platform identifier (e.g., &quot;MAUI&quot;, &quot;WPF&quot;)</td>
</tr>
<tr>
<td><code>baseType</code></td>
<td><code>string</code></td>
<td>Fully qualified base type name</td>
</tr>
</tbody>
</table>
<h4 id="usage-examples-6">Usage Examples</h4>
<pre><code class="lang-csharp">// In AssemblyInfo.cs
[assembly: PageUIComponentBaseType(&quot;MAUI&quot;, &quot;Microsoft.Maui.Controls.Page&quot;)]
[assembly: PageUIComponentBaseType(&quot;WPF&quot;, &quot;System.Windows.Window&quot;)]
</code></pre>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/BretJohnson/hot-preview/blob/main/docs/docs/attributes.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
