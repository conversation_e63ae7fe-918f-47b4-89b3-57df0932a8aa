{"api/HotPreview.AutoGeneratePreviewAttribute.html": {"href": "api/HotPreview.AutoGeneratePreviewAttribute.html", "title": "Class AutoGeneratePreviewAttribute | Hot Preview Documentation", "summary": "Class AutoGeneratePreviewAttribute Namespace HotPreview Assembly HotPreview.dll Controls whether auto-generated previews should be created for a UI component. [AttributeUsage(AttributeTargets.Class)] public sealed class AutoGeneratePreviewAttribute : Attribute Inheritance object Attribute AutoGeneratePreviewAttribute Inherited Members Attribute.Equals(object) Attribute.GetCustomAttribute(Assembly, Type) Attribute.GetCustomAttribute(Assembly, Type, bool) Attribute.GetCustomAttribute(MemberInfo, Type) Attribute.GetCustomAttribute(MemberInfo, Type, bool) Attribute.GetCustomAttribute(Module, Type) Attribute.GetCustomAttribute(Module, Type, bool) Attribute.GetCustomAttribute(ParameterInfo, Type) Attribute.GetCustomAttribute(ParameterInfo, Type, bool) Attribute.GetCustomAttributes(Assembly) Attribute.GetCustomAttributes(Assembly, bool) Attribute.GetCustomAttributes(Assembly, Type) Attribute.GetCustomAttributes(Assembly, Type, bool) Attribute.GetCustomAttributes(MemberInfo) Attribute.GetCustomAttributes(MemberInfo, bool) Attribute.GetCustomAttributes(MemberInfo, Type) Attribute.GetCustomAttributes(MemberInfo, Type, bool) Attribute.GetCustomAttributes(Module) Attribute.GetCustomAttributes(Module, bool) Attribute.GetCustomAttributes(Module, Type) Attribute.GetCustomAttributes(Module, Type, bool) Attribute.GetCustomAttributes(ParameterInfo) Attribute.GetCustomAttributes(ParameterInfo, bool) Attribute.GetCustomAttributes(ParameterInfo, Type) Attribute.GetCustomAttributes(ParameterInfo, Type, bool) Attribute.GetHashCode() Attribute.IsDefaultAttribute() Attribute.IsDefined(Assembly, Type) Attribute.IsDefined(Assembly, Type, bool) Attribute.IsDefined(MemberInfo, Type) Attribute.IsDefined(MemberInfo, Type, bool) Attribute.IsDefined(Module, Type) Attribute.IsDefined(Module, Type, bool) Attribute.IsDefined(ParameterInfo, Type) Attribute.IsDefined(ParameterInfo, Type, bool) Attribute.Match(object) Attribute.TypeId object.Equals(object, object) object.GetType() object.ReferenceEquals(object, object) object.ToString() Remarks When present on a class and the autoGenerate property is false, auto-generation is disabled. Constructors AutoGeneratePreviewAttribute(bool) Controls whether auto-generated previews should be created for a UI component. public AutoGeneratePreviewAttribute(bool autoGenerate) Parameters autoGenerate bool Controls whether auto-generated previews should be created for this component. When set to false, auto-generation is disabled. Remarks When present on a class and the autoGenerate property is false, auto-generation is disabled. Properties AutoGenerate Controls whether auto-generated previews should be created for this component. When set to false, auto-generation is disabled. public bool AutoGenerate { get; } Property Value bool"}, "api/HotPreview.ControlUIComponentBaseTypeAttribute.html": {"href": "api/HotPreview.ControlUIComponentBaseTypeAttribute.html", "title": "Class ControlUIComponentBaseTypeAttribute | Hot Preview Documentation", "summary": "Class ControlUIComponentBaseTypeAttribute Namespace HotPreview Assembly HotPreview.dll Specifies the base type for control UI components on a specific platform. [AttributeUsage(AttributeTargets.Assembly, AllowMultiple = true)] public sealed class ControlUIComponentBaseTypeAttribute : Attribute Inheritance object Attribute ControlUIComponentBaseTypeAttribute Inherited Members Attribute.Equals(object) Attribute.GetCustomAttribute(Assembly, Type) Attribute.GetCustomAttribute(Assembly, Type, bool) Attribute.GetCustomAttribute(MemberInfo, Type) Attribute.GetCustomAttribute(MemberInfo, Type, bool) Attribute.GetCustomAttribute(Module, Type) Attribute.GetCustomAttribute(Module, Type, bool) Attribute.GetCustomAttribute(ParameterInfo, Type) Attribute.GetCustomAttribute(ParameterInfo, Type, bool) Attribute.GetCustomAttributes(Assembly) Attribute.GetCustomAttributes(Assembly, bool) Attribute.GetCustomAttributes(Assembly, Type) Attribute.GetCustomAttributes(Assembly, Type, bool) Attribute.GetCustomAttributes(MemberInfo) Attribute.GetCustomAttributes(MemberInfo, bool) Attribute.GetCustomAttributes(MemberInfo, Type) Attribute.GetCustomAttributes(MemberInfo, Type, bool) Attribute.GetCustomAttributes(Module) Attribute.GetCustomAttributes(Module, bool) Attribute.GetCustomAttributes(Module, Type) Attribute.GetCustomAttributes(Module, Type, bool) Attribute.GetCustomAttributes(ParameterInfo) Attribute.GetCustomAttributes(ParameterInfo, bool) Attribute.GetCustomAttributes(ParameterInfo, Type) Attribute.GetCustomAttributes(ParameterInfo, Type, bool) Attribute.GetHashCode() Attribute.IsDefaultAttribute() Attribute.IsDefined(Assembly, Type) Attribute.IsDefined(Assembly, Type, bool) Attribute.IsDefined(MemberInfo, Type) Attribute.IsDefined(MemberInfo, Type, bool) Attribute.IsDefined(Module, Type) Attribute.IsDefined(Module, Type, bool) Attribute.IsDefined(ParameterInfo, Type) Attribute.IsDefined(ParameterInfo, Type, bool) Attribute.Match(object) Attribute.TypeId object.Equals(object, object) object.GetType() object.ReferenceEquals(object, object) object.ToString() Constructors ControlUIComponentBaseTypeAttribute(string, string) Specifies the base type for control UI components on a specific platform. public ControlUIComponentBaseTypeAttribute(string platform, string baseType) Parameters platform string baseType string Properties BaseType public string BaseType { get; } Property Value string Platform public string Platform { get; } Property Value string"}, "api/HotPreview.PageUIComponentBaseTypeAttribute.html": {"href": "api/HotPreview.PageUIComponentBaseTypeAttribute.html", "title": "Class PageUIComponentBaseTypeAttribute | Hot Preview Documentation", "summary": "Class PageUIComponentBaseTypeAttribute Namespace HotPreview Assembly HotPreview.dll Specifies the base type for page UI components on a specific platform. [AttributeUsage(AttributeTargets.Assembly, AllowMultiple = true)] public sealed class PageUIComponentBaseTypeAttribute : Attribute Inheritance object Attribute PageUIComponentBaseTypeAttribute Inherited Members Attribute.Equals(object) Attribute.GetCustomAttribute(Assembly, Type) Attribute.GetCustomAttribute(Assembly, Type, bool) Attribute.GetCustomAttribute(MemberInfo, Type) Attribute.GetCustomAttribute(MemberInfo, Type, bool) Attribute.GetCustomAttribute(Module, Type) Attribute.GetCustomAttribute(Module, Type, bool) Attribute.GetCustomAttribute(ParameterInfo, Type) Attribute.GetCustomAttribute(ParameterInfo, Type, bool) Attribute.GetCustomAttributes(Assembly) Attribute.GetCustomAttributes(Assembly, bool) Attribute.GetCustomAttributes(Assembly, Type) Attribute.GetCustomAttributes(Assembly, Type, bool) Attribute.GetCustomAttributes(MemberInfo) Attribute.GetCustomAttributes(MemberInfo, bool) Attribute.GetCustomAttributes(MemberInfo, Type) Attribute.GetCustomAttributes(MemberInfo, Type, bool) Attribute.GetCustomAttributes(Module) Attribute.GetCustomAttributes(Module, bool) Attribute.GetCustomAttributes(Module, Type) Attribute.GetCustomAttributes(Module, Type, bool) Attribute.GetCustomAttributes(ParameterInfo) Attribute.GetCustomAttributes(ParameterInfo, bool) Attribute.GetCustomAttributes(ParameterInfo, Type) Attribute.GetCustomAttributes(ParameterInfo, Type, bool) Attribute.GetHashCode() Attribute.IsDefaultAttribute() Attribute.IsDefined(Assembly, Type) Attribute.IsDefined(Assembly, Type, bool) Attribute.IsDefined(MemberInfo, Type) Attribute.IsDefined(MemberInfo, Type, bool) Attribute.IsDefined(Module, Type) Attribute.IsDefined(Module, Type, bool) Attribute.IsDefined(ParameterInfo, Type) Attribute.IsDefined(ParameterInfo, Type, bool) Attribute.Match(object) Attribute.TypeId object.Equals(object, object) object.GetType() object.ReferenceEquals(object, object) object.ToString() Constructors PageUIComponentBaseTypeAttribute(string, string) Specifies the base type for page UI components on a specific platform. public PageUIComponentBaseTypeAttribute(string platform, string baseType) Parameters platform string baseType string Properties BaseType public string BaseType { get; } Property Value string Platform public string Platform { get; } Property Value string"}, "api/HotPreview.PreviewAttribute-1.html": {"href": "api/HotPreview.PreviewAttribute-1.html", "title": "Class PreviewAttribute<TUIComponent> | Hot Preview Documentation", "summary": "Class PreviewAttribute<TUIComponent> Namespace HotPreview Assembly HotPreview.dll Specifies that this static method creates a preview for a UI component with an explicitly specified UI component type. [AttributeUsage(AttributeTargets.Class|AttributeTargets.Method)] public sealed class PreviewAttribute<TUIComponent> : PreviewAttribute Type Parameters TUIComponent The UI component type that this preview is associated with. Inheritance object Attribute PreviewAttribute PreviewAttribute<TUIComponent> Inherited Members PreviewAttribute.DisplayName PreviewAttribute.UIComponentType Attribute.Equals(object) Attribute.GetCustomAttribute(Assembly, Type) Attribute.GetCustomAttribute(Assembly, Type, bool) Attribute.GetCustomAttribute(MemberInfo, Type) Attribute.GetCustomAttribute(MemberInfo, Type, bool) Attribute.GetCustomAttribute(Module, Type) Attribute.GetCustomAttribute(Module, Type, bool) Attribute.GetCustomAttribute(ParameterInfo, Type) Attribute.GetCustomAttribute(ParameterInfo, Type, bool) Attribute.GetCustomAttributes(Assembly) Attribute.GetCustomAttributes(Assembly, bool) Attribute.GetCustomAttributes(Assembly, Type) Attribute.GetCustomAttributes(Assembly, Type, bool) Attribute.GetCustomAttributes(MemberInfo) Attribute.GetCustomAttributes(MemberInfo, bool) Attribute.GetCustomAttributes(MemberInfo, Type) Attribute.GetCustomAttributes(MemberInfo, Type, bool) Attribute.GetCustomAttributes(Module) Attribute.GetCustomAttributes(Module, bool) Attribute.GetCustomAttributes(Module, Type) Attribute.GetCustomAttributes(Module, Type, bool) Attribute.GetCustomAttributes(ParameterInfo) Attribute.GetCustomAttributes(ParameterInfo, bool) Attribute.GetCustomAttributes(ParameterInfo, Type) Attribute.GetCustomAttributes(ParameterInfo, Type, bool) Attribute.GetHashCode() Attribute.IsDefaultAttribute() Attribute.IsDefined(Assembly, Type) Attribute.IsDefined(Assembly, Type, bool) Attribute.IsDefined(MemberInfo, Type) Attribute.IsDefined(MemberInfo, Type, bool) Attribute.IsDefined(Module, Type) Attribute.IsDefined(Module, Type, bool) Attribute.IsDefined(ParameterInfo, Type) Attribute.IsDefined(ParameterInfo, Type, bool) Attribute.Match(object) Attribute.TypeId object.Equals(object, object) object.GetType() object.ReferenceEquals(object, object) object.ToString() Remarks This generic version is only needed when it's necessary to explicitly specify the UI component type. The type parameter TUIComponent specifies the UI component type that this preview is associated with. The method should return the preview UI object and the Hot Preview platform code will then navigate to that preview. If the method navigates to the preview itself (often true for a single page app) it can return void. Constructors PreviewAttribute(string?) Specifies that this static method creates a preview for a UI component with an explicitly specified UI component type. public PreviewAttribute(string? displayName = null) Parameters displayName string Optional override for the display name of the preview, determining how it appears in the navigation UI. If not specified, the name of the method (or class, for class-based previews) is used, converted to start case (e.g. \"MyPreview\" becomes \"My Preview\"). Storybook also uses this same start case convention. Remarks This generic version is only needed when it's necessary to explicitly specify the UI component type. The type parameter TUIComponent specifies the UI component type that this preview is associated with. The method should return the preview UI object and the Hot Preview platform code will then navigate to that preview. If the method navigates to the preview itself (often true for a single page app) it can return void."}, "api/HotPreview.PreviewAttribute.html": {"href": "api/HotPreview.PreviewAttribute.html", "title": "Class PreviewAttribute | Hot Preview Documentation", "summary": "Class PreviewAttribute Namespace HotPreview Assembly HotPreview.dll Specifies that this static method creates a preview for a UI component. [AttributeUsage(AttributeTargets.Class|AttributeTargets.Method)] public class PreviewAttribute : Attribute Inheritance object Attribute PreviewAttribute Derived PreviewAttribute<TUIComponent> Inherited Members Attribute.Equals(object) Attribute.GetCustomAttribute(Assembly, Type) Attribute.GetCustomAttribute(Assembly, Type, bool) Attribute.GetCustomAttribute(MemberInfo, Type) Attribute.GetCustomAttribute(MemberInfo, Type, bool) Attribute.GetCustomAttribute(Module, Type) Attribute.GetCustomAttribute(Module, Type, bool) Attribute.GetCustomAttribute(ParameterInfo, Type) Attribute.GetCustomAttribute(ParameterInfo, Type, bool) Attribute.GetCustomAttributes(Assembly) Attribute.GetCustomAttributes(Assembly, bool) Attribute.GetCustomAttributes(Assembly, Type) Attribute.GetCustomAttributes(Assembly, Type, bool) Attribute.GetCustomAttributes(MemberInfo) Attribute.GetCustomAttributes(MemberInfo, bool) Attribute.GetCustomAttributes(MemberInfo, Type) Attribute.GetCustomAttributes(MemberInfo, Type, bool) Attribute.GetCustomAttributes(Module) Attribute.GetCustomAttributes(Module, bool) Attribute.GetCustomAttributes(Module, Type) Attribute.GetCustomAttributes(Module, Type, bool) Attribute.GetCustomAttributes(ParameterInfo) Attribute.GetCustomAttributes(ParameterInfo, bool) Attribute.GetCustomAttributes(ParameterInfo, Type) Attribute.GetCustomAttributes(ParameterInfo, Type, bool) Attribute.GetHashCode() Attribute.IsDefaultAttribute() Attribute.IsDefined(Assembly, Type) Attribute.IsDefined(Assembly, Type, bool) Attribute.IsDefined(MemberInfo, Type) Attribute.IsDefined(MemberInfo, Type, bool) Attribute.IsDefined(Module, Type) Attribute.IsDefined(Module, Type, bool) Attribute.IsDefined(ParameterInfo, Type) Attribute.IsDefined(ParameterInfo, Type, bool) Attribute.Match(object) Attribute.TypeId object.Equals(object, object) object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Remarks The method should return the preview UI object and the Hot Preview platform code will then navigate to that preview. If the method navigates to the preview itself (often true for a single page app) it can return void. The UI component type is automatically inferred from the method return type or, if the method returns void, from the containing class. If you need to explicitly specify a different UI component type, use the generic PreviewAttribute<TUIComponent> instead. Constructors PreviewAttribute(string?) Specifies that this static method creates a preview for a UI component. public PreviewAttribute(string? displayName = null) Parameters displayName string Optional override for the display name of the preview, determining how it appears in the navigation UI. If not specified, the name of the method (or class, for class-based previews) is used, converted to start case (e.g. \"MyPreview\" becomes \"My Preview\"). Storybook also uses this start case convention. Remarks The method should return the preview UI object and the Hot Preview platform code will then navigate to that preview. If the method navigates to the preview itself (often true for a single page app) it can return void. The UI component type is automatically inferred from the method return type or, if the method returns void, from the containing class. If you need to explicitly specify a different UI component type, use the generic PreviewAttribute<TUIComponent> instead. PreviewAttribute(string?, Type?) protected PreviewAttribute(string? displayName, Type? uiComponentType) Parameters displayName string uiComponentType Type Properties DisplayName Optional override for the display name of the preview, determining how it appears in the navigation UI. If not specified, the name of the method (or class, for class-based previews) is used, converted to start case (e.g. \"MyPreview\" becomes \"My Preview\"). Storybook also uses this start case convention. public string? DisplayName { get; } Property Value string UIComponentType public Type? UIComponentType { get; } Property Value Type"}, "api/HotPreview.PreviewCommandAttribute.html": {"href": "api/HotPreview.PreviewCommandAttribute.html", "title": "Class PreviewCommandAttribute | Hot Preview Documentation", "summary": "Class PreviewCommandAttribute Namespace HotPreview Assembly HotPreview.dll Designates this method as a command that can be executed from the DevTools UI or as an MCP tool command. [AttributeUsage(AttributeTargets.Method)] public sealed class PreviewCommandAttribute : Attribute Inheritance object Attribute PreviewCommandAttribute Inherited Members Attribute.Equals(object) Attribute.GetCustomAttribute(Assembly, Type) Attribute.GetCustomAttribute(Assembly, Type, bool) Attribute.GetCustomAttribute(MemberInfo, Type) Attribute.GetCustomAttribute(MemberInfo, Type, bool) Attribute.GetCustomAttribute(Module, Type) Attribute.GetCustomAttribute(Module, Type, bool) Attribute.GetCustomAttribute(ParameterInfo, Type) Attribute.GetCustomAttribute(ParameterInfo, Type, bool) Attribute.GetCustomAttributes(Assembly) Attribute.GetCustomAttributes(Assembly, bool) Attribute.GetCustomAttributes(Assembly, Type) Attribute.GetCustomAttributes(Assembly, Type, bool) Attribute.GetCustomAttributes(MemberInfo) Attribute.GetCustomAttributes(MemberInfo, bool) Attribute.GetCustomAttributes(MemberInfo, Type) Attribute.GetCustomAttributes(MemberInfo, Type, bool) Attribute.GetCustomAttributes(Module) Attribute.GetCustomAttributes(Module, bool) Attribute.GetCustomAttributes(Module, Type) Attribute.GetCustomAttributes(Module, Type, bool) Attribute.GetCustomAttributes(ParameterInfo) Attribute.GetCustomAttributes(ParameterInfo, bool) Attribute.GetCustomAttributes(ParameterInfo, Type) Attribute.GetCustomAttributes(ParameterInfo, Type, bool) Attribute.GetHashCode() Attribute.IsDefaultAttribute() Attribute.IsDefined(Assembly, Type) Attribute.IsDefined(Assembly, Type, bool) Attribute.IsDefined(MemberInfo, Type) Attribute.IsDefined(MemberInfo, Type, bool) Attribute.IsDefined(Module, Type) Attribute.IsDefined(Module, Type, bool) Attribute.IsDefined(ParameterInfo, Type) Attribute.IsDefined(ParameterInfo, Type, bool) Attribute.Match(object) Attribute.TypeId object.Equals(object, object) object.GetType() object.ReferenceEquals(object, object) object.ToString() Remarks Commands typically update global state, changing the way that subsequent previews appear. For instance, commands could update the UI language for the app or switch the theme between light and dark. Commands normally don't update UI themselves (but they can - nothing prevents this). Commands for now should have no parameters and return void, though likely we'll add parameter and return support in the future once we've figured out the desired behavior. Constructors PreviewCommandAttribute(string?) Designates this method as a command that can be executed from the DevTools UI or as an MCP tool command. public PreviewCommandAttribute(string? displayName = null) Parameters displayName string Optional display name override for the command, determining how it appears in navigation UI. If not specified, the name of the method is used. Remarks Commands typically update global state, changing the way that subsequent previews appear. For instance, commands could update the UI language for the app or switch the theme between light and dark. Commands normally don't update UI themselves (but they can - nothing prevents this). Commands for now should have no parameters and return void, though likely we'll add parameter and return support in the future once we've figured out the desired behavior. Properties DisplayName Optional display name override for the command, determining how it appears in navigation UI. If not specified, the name of the method is used. public string? DisplayName { get; } Property Value string"}, "api/HotPreview.RoutePreview-1.html": {"href": "api/HotPreview.RoutePreview-1.html", "title": "Class RoutePreview<T> | Hot Preview Documentation", "summary": "Class RoutePreview<T> Namespace HotPreview Assembly HotPreview.dll Represents a strongly-typed preview for a route-based navigation. public class RoutePreview<T> : RoutePreview where T : class Type Parameters T The type associated with this route preview. Inheritance object RoutePreview RoutePreview<T> Inherited Members RoutePreview.Route object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Constructors RoutePreview(string) Represents a strongly-typed preview for a route-based navigation. public RoutePreview(string route) Parameters route string"}, "api/HotPreview.RoutePreview.html": {"href": "api/HotPreview.RoutePreview.html", "title": "Class RoutePreview | Hot Preview Documentation", "summary": "Class RoutePreview Namespace HotPreview Assembly HotPreview.dll Represents a preview for a route-based navigation. public class RoutePreview Inheritance object RoutePreview Derived RoutePreview<T> Inherited Members object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Constructors RoutePreview(string) Represents a preview for a route-based navigation. public RoutePreview(string route) Parameters route string Properties Route public string Route { get; } Property Value string"}, "api/HotPreview.SpecialUIComponentNames.html": {"href": "api/HotPreview.SpecialUIComponentNames.html", "title": "Class SpecialUIComponentNames | Hot Preview Documentation", "summary": "Class SpecialUIComponentNames Namespace HotPreview Assembly HotPreview.dll public static class SpecialUIComponentNames Inheritance object SpecialUIComponentNames Inherited Members object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Fields FullApp Navigating to this component switches to \"full app\" mode. public const string FullApp = \"$FullApp\" Field Value string"}, "api/HotPreview.UIComponentAttribute.html": {"href": "api/HotPreview.UIComponentAttribute.html", "title": "Class UIComponentAttribute | Hot Preview Documentation", "summary": "Class UIComponentAttribute Namespace HotPreview Assembly HotPreview.dll Specifies that this class is a UI component. [AttributeUsage(AttributeTargets.Class)] public sealed class UIComponentAttribute : Attribute Inheritance object Attribute UIComponentAttribute Inherited Members Attribute.Equals(object) Attribute.GetCustomAttribute(Assembly, Type) Attribute.GetCustomAttribute(Assembly, Type, bool) Attribute.GetCustomAttribute(MemberInfo, Type) Attribute.GetCustomAttribute(MemberInfo, Type, bool) Attribute.GetCustomAttribute(Module, Type) Attribute.GetCustomAttribute(Module, Type, bool) Attribute.GetCustomAttribute(ParameterInfo, Type) Attribute.GetCustomAttribute(ParameterInfo, Type, bool) Attribute.GetCustomAttributes(Assembly) Attribute.GetCustomAttributes(Assembly, bool) Attribute.GetCustomAttributes(Assembly, Type) Attribute.GetCustomAttributes(Assembly, Type, bool) Attribute.GetCustomAttributes(MemberInfo) Attribute.GetCustomAttributes(MemberInfo, bool) Attribute.GetCustomAttributes(MemberInfo, Type) Attribute.GetCustomAttributes(MemberInfo, Type, bool) Attribute.GetCustomAttributes(Module) Attribute.GetCustomAttributes(Module, bool) Attribute.GetCustomAttributes(Module, Type) Attribute.GetCustomAttributes(Module, Type, bool) Attribute.GetCustomAttributes(ParameterInfo) Attribute.GetCustomAttributes(ParameterInfo, bool) Attribute.GetCustomAttributes(ParameterInfo, Type) Attribute.GetCustomAttributes(ParameterInfo, Type, bool) Attribute.GetHashCode() Attribute.IsDefaultAttribute() Attribute.IsDefined(Assembly, Type) Attribute.IsDefined(Assembly, Type, bool) Attribute.IsDefined(MemberInfo, Type) Attribute.IsDefined(MemberInfo, Type, bool) Attribute.IsDefined(Module, Type) Attribute.IsDefined(Module, Type, bool) Attribute.IsDefined(ParameterInfo, Type) Attribute.IsDefined(ParameterInfo, Type, bool) Attribute.Match(object) Attribute.TypeId object.Equals(object, object) object.GetType() object.ReferenceEquals(object, object) object.ToString() Remarks Normally UI components don't need to be defined explicitly (defining a preview is sufficient), but this can be used to define a display name for the component. Constructors UIComponentAttribute(string?) Specifies that this class is a UI component. public UIComponentAttribute(string? displayName = null) Parameters displayName string Optional override for the display name for the UI component. If not specified, the name of the class is used (with no namespace). Remarks Normally UI components don't need to be defined explicitly (defining a preview is sufficient), but this can be used to define a display name for the component. Properties DisplayName Optional override for the display name for the UI component. If not specified, the name of the class is used (with no namespace). public string? DisplayName { get; } Property Value string"}, "api/HotPreview.UIComponentCategoryAttribute.html": {"href": "api/HotPreview.UIComponentCategoryAttribute.html", "title": "Class UIComponentCategoryAttribute | Hot Preview Documentation", "summary": "Class UIComponentCategoryAttribute Namespace HotPreview Assembly HotPreview.dll Specifies the category name for a set of UI components. [AttributeUsage(AttributeTargets.Assembly, AllowMultiple = true)] public sealed class UIComponentCategoryAttribute : Attribute Inheritance object Attribute UIComponentCategoryAttribute Inherited Members Attribute.Equals(object) Attribute.GetCustomAttribute(Assembly, Type) Attribute.GetCustomAttribute(Assembly, Type, bool) Attribute.GetCustomAttribute(MemberInfo, Type) Attribute.GetCustomAttribute(MemberInfo, Type, bool) Attribute.GetCustomAttribute(Module, Type) Attribute.GetCustomAttribute(Module, Type, bool) Attribute.GetCustomAttribute(ParameterInfo, Type) Attribute.GetCustomAttribute(ParameterInfo, Type, bool) Attribute.GetCustomAttributes(Assembly) Attribute.GetCustomAttributes(Assembly, bool) Attribute.GetCustomAttributes(Assembly, Type) Attribute.GetCustomAttributes(Assembly, Type, bool) Attribute.GetCustomAttributes(MemberInfo) Attribute.GetCustomAttributes(MemberInfo, bool) Attribute.GetCustomAttributes(MemberInfo, Type) Attribute.GetCustomAttributes(MemberInfo, Type, bool) Attribute.GetCustomAttributes(Module) Attribute.GetCustomAttributes(Module, bool) Attribute.GetCustomAttributes(Module, Type) Attribute.GetCustomAttributes(Module, Type, bool) Attribute.GetCustomAttributes(ParameterInfo) Attribute.GetCustomAttributes(ParameterInfo, bool) Attribute.GetCustomAttributes(ParameterInfo, Type) Attribute.GetCustomAttributes(ParameterInfo, Type, bool) Attribute.GetHashCode() Attribute.IsDefaultAttribute() Attribute.IsDefined(Assembly, Type) Attribute.IsDefined(Assembly, Type, bool) Attribute.IsDefined(MemberInfo, Type) Attribute.IsDefined(MemberInfo, Type, bool) Attribute.IsDefined(Module, Type) Attribute.IsDefined(Module, Type, bool) Attribute.IsDefined(ParameterInfo, Type) Attribute.IsDefined(ParameterInfo, Type, bool) Attribute.Match(object) Attribute.TypeId object.Equals(object, object) object.GetType() object.ReferenceEquals(object, object) object.ToString() Remarks Categories are just used for display purposes. If no category is specified for a component, the category name defaults to \"Pages\" or \"Controls\", depending on whether the UI component is a page or not. This attribute can be specified multiple times for a single category, in which case the UI components are combined together. Constructors UIComponentCategoryAttribute(string, params Type[]) Specifies the category name for a set of UI components. public UIComponentCategoryAttribute(string name, params Type[] uiComponents) Parameters name string The name of the category. uiComponents Type[] The UI components that belong to this category. Remarks Categories are just used for display purposes. If no category is specified for a component, the category name defaults to \"Pages\" or \"Controls\", depending on whether the UI component is a page or not. This attribute can be specified multiple times for a single category, in which case the UI components are combined together. Properties Name public string Name { get; } Property Value string UIComponentTypes public Type[] UIComponentTypes { get; } Property Value Type[]"}, "api/HotPreview.html": {"href": "api/HotPreview.html", "title": "Namespace HotPreview | Hot Preview Documentation", "summary": "Namespace HotPreview Classes AutoGeneratePreviewAttribute Controls whether auto-generated previews should be created for a UI component. ControlUIComponentBaseTypeAttribute Specifies the base type for control UI components on a specific platform. PageUIComponentBaseTypeAttribute Specifies the base type for page UI components on a specific platform. PreviewAttribute Specifies that this static method creates a preview for a UI component. PreviewAttribute<TUIComponent> Specifies that this static method creates a preview for a UI component with an explicitly specified UI component type. PreviewCommandAttribute Designates this method as a command that can be executed from the DevTools UI or as an MCP tool command. RoutePreview Represents a preview for a route-based navigation. RoutePreview<T> Represents a strongly-typed preview for a route-based navigation. SpecialUIComponentNames UIComponentAttribute Specifies that this class is a UI component. UIComponentCategoryAttribute Specifies the category name for a set of UI components."}, "docs/architecture.html": {"href": "docs/architecture.html", "title": "Architecture | Hot Preview Documentation", "summary": "Architecture HotPreview is designed as a cross-platform UI component preview system for .NET, with a modular architecture that separates concerns and enables platform-specific implementations. System Overview graph LR subgraph \"DevTools Process\" DT[\"DevTools<br/>(Uno+Skia)\"] end subgraph \"Your App Process\" YA[\"Your App\"] HPL[\"Hot Preview<br/>Platform Library\"] YA -.-> HPL end DT -.->|\"JSON-RPC Protocol\"| YA Core Components 1. HotPreview Core Library Location: src/HotPreview/ Purpose: Base attributes and types for defining previews Key Components: PreviewAttribute - Marks methods as preview definitions UIComponentAttribute - Explicitly marks classes as UI components PreviewCommandAttribute - Defines executable commands AutoGeneratePreviewAttribute - Controls auto-generation behavior 2. HotPreview Shared Model Location: src/HotPreview.SharedModel/ Purpose: Cross-platform protocol and reflection utilities Key Components: Protocol Definitions: JSON-RPC interfaces for tool communication Reflection Utilities: Component and preview discovery at runtime App Services: Base classes for platform-specific implementations Protocol Classes: public interface IPreviewAppService { Task<PreviewInfo[]> GetPreviewsAsync(); Task NavigateToPreviewAsync(string previewId); Task ExecuteCommandAsync(string commandId); } 3. Platform Applications Location: src/platforms/ Purpose: Platform-specific preview applications MAUI Implementation (HotPreview.App.Maui) MauiPreviewApplication - Main application service MauiPreviewNavigatorService - Navigation handling PreviewsPage - UI for displaying component tree Platform-specific resource handling WPF Implementation (HotPreview.App.Wpf) (Planned) Similar structure adapted for WPF platform Windows-specific navigation and rendering 4. DevTools Infrastructure Location: src/tooling/ Purpose: Visual development environment and tooling DevTools Application (HotPreview.DevToolsApp) Uno+Skia Desktop App: Main user interface Connection Management: Handles multiple app connections Component Tree View: Hierarchical navigation interface Command Execution: UI for running preview commands DevTools Launcher (HotPreview.DevTools) Global Tool: dotnet tool install -g HotPreview.DevTools Process Management: Launches and manages DevTools instances Single Instance Logic: Prevents multiple DevTools instances Tooling Infrastructure (HotPreview.Tooling) Roslyn Analysis: Component discovery through source analysis JSON-RPC Server: Communication protocol implementation MCP Server: AI integration capabilities Visual Testing: Screenshot and comparison utilities Communication Protocol JSON-RPC Over Pipes HotPreview uses JSON-RPC over named pipes for communication between DevTools and applications. Message Flow: App Startup: Application connects to DevTools via named pipe Discovery: DevTools requests component and preview information Navigation: User clicks component, DevTools sends navigation command Command Execution: User triggers command, DevTools forwards to app Example Messages: // Get Previews Request { \"id\": 1, \"method\": \"getPreviewsAsync\", \"params\": {} } // Navigate to Preview Request { \"id\": 2, \"method\": \"navigateToPreviewAsync\", \"params\": { \"previewId\": \"MyApp.Views.ProductCard.Preview\" } } Component Discovery Runtime Discovery (Reflection) Location: HotPreview.SharedModel/App/GetPreviewsViaReflection.cs Process: Assembly Scanning: Scan loaded assemblies for components Type Analysis: Identify types inheriting from platform base types Attribute Detection: Find [Preview] and [UIComponent] attributes Auto-Generation: Create default previews for discovered components Example Code: public static PreviewInfo[] GetPreviews(Assembly assembly) { var types = assembly.GetTypes() .Where(t => IsUIComponent(t)) .ToArray(); return types.SelectMany(CreatePreviewsForType).ToArray(); } Build-Time Discovery (Roslyn) Location: HotPreview.Tooling/GetPreviewsFromRoslyn.cs Process: Source Analysis: Parse C# source files using Roslyn Syntax Tree Walking: Find class declarations and attributes Preview Method Detection: Locate [Preview] methods Metadata Generation: Create preview metadata without runtime loading Benefits: No runtime overhead Works with uncompiled code Better performance for large applications MSBuild Integration Build Tasks Location: src/HotPreview.AppBuildTasks/ Key Tasks: GeneratePreviewAppSettingsTask - Creates app configuration DevTools Launch: Automatically starts DevTools during builds Symbol Definition: Ensures PREVIEWS symbol is defined in Debug builds Integration Points: <Target Name=\"LaunchDevTools\" BeforeTargets=\"Build\" Condition=\"$(Configuration) == 'Debug'\"> <Exec Command=\"hotpreview\" ContinueOnError=\"true\" /> </Target> Platform Abstraction Base Type Configuration Platform-specific base types are configured via assembly attributes: [assembly: ControlUIComponentBaseType(\"MAUI\", \"Microsoft.Maui.Controls.View\")] [assembly: PageUIComponentBaseType(\"MAUI\", \"Microsoft.Maui.Controls.Page\")] Service Implementation Each platform implements the core interfaces: public class MauiPreviewApplication : PreviewApplication { protected override IPreviewNavigator CreateNavigator() => new MauiPreviewNavigatorService(); protected override IUIComponentExclusionFilter CreateExclusionFilter() => new MauiUIComponentExclusionFilter(); } AI Integration (MCP Server) Model Context Protocol Location: HotPreview.Tooling/McpServer/ Capabilities: Device Management: Android/iOS device discovery and control Screenshot Capture: Automated visual testing App Management: Launch and control preview applications Preview Generation: AI-assisted component creation Architecture: ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │ AI Agent │◄──►│ MCP Server │◄──►│ HotPreview │ │ (Claude) │ │ │ │ DevTools │ └─────────────┘ └─────────────┘ └─────────────┘ Visual Testing Snapshot Generation Location: HotPreview.Tooling/VisualTestUtils/ Components: IImageEditor - Image manipulation interface IVisualComparer - Visual difference detection VisualRegressionTester - Test orchestration MagickNet Integration: ImageMagick-based image processing Workflow: Capture Screenshot: Take snapshot of component Compare Images: Detect visual differences Generate Report: Create difference visualization Assert Results: Integrate with test frameworks Extension Points Custom Platform Support To add support for a new platform: Implement Base Services: public class MyPlatformPreviewApplication : PreviewApplication { // Platform-specific implementation } Configure Base Types: [assembly: ControlUIComponentBaseType(\"MyPlatform\", \"MyPlatform.Controls.Control\")] Create Package: Distribute as HotPreview.App.MyPlatform Custom Discovery Extend component discovery: public class CustomComponentDiscovery : IComponentDiscovery { public PreviewInfo[] DiscoverPreviews(Assembly assembly) { // Custom discovery logic } } Performance Considerations Memory Management Lazy Loading: Components loaded on-demand Weak References: Prevent memory leaks in long-running sessions Resource Cleanup: Proper disposal of platform resources Communication Optimization Batch Operations: Reduce JSON-RPC round trips Incremental Updates: Send only changed data Connection Pooling: Reuse connections across sessions Scalability Concurrent Discovery: Parallel component analysis Caching: Cache discovered components and metadata Selective Loading: Load only visible components in DevTools"}, "docs/attributes.html": {"href": "docs/attributes.html", "title": "Hot Preview Attributes Reference | Hot Preview Documentation", "summary": "Hot Preview Attributes Reference Hot Preview provides several attributes to control how your UI components are discovered, displayed, and organized in the DevTools interface. This reference covers all available attributes and their usage. Core Attributes PreviewAttribute The [Preview] attribute is the primary way to define custom previews for your UI components. Target: Methods and Classes Namespace: HotPreview Overview The PreviewAttribute comes in two forms: Non-generic: PreviewAttribute - Automatically infers UI component type Generic: PreviewAttribute<TUIComponent> - Explicitly specifies UI component type Parameters Parameter Type Description displayName string? Optional display name override for the preview, determining how it appears in navigation UI. If not specified, uses method/class name converted to start case (e.g., \"MyPreview\" becomes \"My Preview\") Type Inference Method return type: UI component type is inferred from the method's return type Void methods: UI component type is inferred from the containing class Explicit specification: Use PreviewAttribute<TUIComponent> when you need to specify a different UI component type Constructors [Preview] // Typical preview - default display name, automatic UI component type inference [Preview(\"Display Name\")] // Overridden display name, with automatic UI component type inference [Preview<MyComponent>] // Explicitly specified UI component type [Preview<MyComponent>(\"Display Name\")] // Overridden display name, with explicit UI component type Usage Examples #if PREVIEWS // Basic preview with automatic type inference [Preview] public static CardView Preview() => new(PreviewData.GetCards(3)); // Named previews with automatic type inference [Preview(\"0 cards\")] public static CardView NoCards() => new(PreviewData.GetCards(0)); [Preview(\"1 Card\")] public static CardView SingleCard() => new(PreviewData.GetCards(1)); [Preview(\"6 Cards\")] public static CardView MultipleCards() => new(PreviewData.GetCards(6)); // void method - UI component type inferred from containing class public static void AppNeedsUpdateState() { } // Explicit UI component type specification using generic attribute [Preview<ProductView>] public static CustomProductLayoutView CustomProductLayout() => new(PreviewData.GetProduct()); #endif UIComponentAttribute The [UIComponent] attribute allows you to explicitly mark classes as UI components and provide custom display names. Target: Classes Namespace: HotPreview Overview Normally UI components don't need to be defined explicitly (defining a preview is sufficient), but this attribute can be used to define a display name for the component. Parameters Parameter Type Description displayName string? Optional display name override for the UI component. If not specified, the class name is used (without namespace) Usage Examples // Explicit display name for UI component [UIComponent(\"Shopping Cart\")] public partial class CartView : ContentView { // Component implementation } PreviewCommandAttribute The [PreviewCommand] attribute defines commands that can be executed from the DevTools interface. Target: Methods Namespace: HotPreview Parameters Parameter Type Description displayName string? Optional display name override for the command, determining how it appears in navigation UI. If not specified, the method name is used Usage Examples #if PREVIEWS [PreviewCommand] public static void ClearCache() { // Clear application cache } [PreviewCommand(\"Reset Application State\"] public static void ResetAppState() { // Reset global state for testing } #endif Configuration Attributes AutoGeneratePreviewAttribute Controls whether auto-generated previews should be created for a UI component. Target: Classes Namespace: HotPreview Overview When present on a class and the autoGenerate property is false, auto-generation is disabled. This attribute provides explicit control over the auto-generation behavior for UI components. It's appropriate to use if the auto-generated preview doesn't work properly, so you don't want to see it in the UI, and you don't want to define a explicit preview for the component. Parameters Parameter Type Description autoGenerate bool Controls whether auto-generated previews should be created for this component. When set to false, auto-generation is disabled Usage Examples // Disable auto-generated previews for this component [AutoGeneratePreview(false)] public partial class ComplexView : ContentView { // This component won't get an auto-generated preview, even if it has a parameterless constructor } Assembly-Level Attributes UIComponentCategoryAttribute Defines categories for organizing UI components in the DevTools interface. Target: Assembly Namespace: HotPreview Overview Categories are used for display purposes only. If no category is specified for a component, the category name defaults to \"Pages\" or \"Controls\", depending on whether the UI component is a page or not. This attribute can be specified multiple times for a single category, in which case the UI components are combined together. Parameters Parameter Type Description name string The name of the category uiComponents Type[] The UI components that belong to this category Usage Examples // In AssemblyInfo.cs or any source file using HotPreview; [assembly: UIComponentCategory(\"Navigation\", typeof(HeaderView), typeof(FooterView), typeof(TabBar))] [assembly: UIComponentCategory(\"Cards\", typeof(ProductCard), typeof(CategoryCard), typeof(InfoCard))] [assembly: UIComponentCategory(\"Forms\", typeof(LoginForm), typeof(RegisterForm), typeof(ContactForm))] // Multiple attributes for the same category combine the components [assembly: UIComponentCategory(\"Data Display\", typeof(ProductCard), typeof(UserProfile))] [assembly: UIComponentCategory(\"Data Display\", typeof(StatisticsView), typeof(ChartView))] ControlUIComponentBaseTypeAttribute Specifies the base type for control UI components on a specific platform. Target: Assembly Namespace: HotPreview Parameters Parameter Type Description platform string Platform identifier (e.g., \"MAUI\", \"WPF\") baseType string Fully qualified base type name Usage Examples // In AssemblyInfo.cs [assembly: ControlUIComponentBaseType(\"MAUI\", \"Microsoft.Maui.Controls.View\")] [assembly: ControlUIComponentBaseType(\"WPF\", \"System.Windows.Controls.Control\")] PageUIComponentBaseTypeAttribute Specifies the base type for page UI components on a specific platform. Target: Assembly Namespace: HotPreview Parameters Parameter Type Description platform string Platform identifier (e.g., \"MAUI\", \"WPF\") baseType string Fully qualified base type name Usage Examples // In AssemblyInfo.cs [assembly: PageUIComponentBaseType(\"MAUI\", \"Microsoft.Maui.Controls.Page\")] [assembly: PageUIComponentBaseType(\"WPF\", \"System.Windows.Window\")]"}, "docs/features.html": {"href": "docs/features.html", "title": "Features | Hot Preview Documentation", "summary": "Features HotPreview provides a comprehensive set of features for efficient UI development and testing in .NET applications. Core Features 🚀 Streamlined Navigation Jump directly to specific UI pages and components without navigating through multiple app screens. Benefits: Dramatically reduces development time Eliminates repetitive manual testing workflows Enables instant access to any component state How it works: DevTools displays a hierarchical tree of all UI components Click any component to navigate directly to it in your running app No need to manually navigate through complex app flows 🔄 Multi-state Testing Quickly visualize UI components with different data inputs and states, ensuring responsive and robust interfaces across all scenarios. Supported scenarios: Empty states Loading states Error conditions Different data volumes (single item, multiple items, large datasets) Various user permissions or app states Example: #if PREVIEWS [Preview(\"Empty Cart\")] public static CartView EmptyCart() => new(PreviewData.GetCart(0)); [Preview(\"Single Item\")] public static CartView SingleItem() => new(PreviewData.GetCart(1)); [Preview(\"Full Cart\")] public static CartView FullCart() => new(PreviewData.GetCart(10)); [Preview(\"Cart with Error\")] public static CartView ErrorCart() => new(PreviewData.GetCartWithError()); #endif 📱 Cross-Platform Visualization View your UI on multiple platforms simultaneously, enabling instant cross-platform comparison and consistency validation. Capabilities: Run the same app on Windows, Android, iOS side-by-side Navigate to the same component across platforms instantly Compare visual consistency and behavior Test platform-specific adaptations 🛠️ DevTools Integration A powerful desktop application that serves as your command center for UI development. DevTools features: Component Tree: Hierarchical view of all UI components Live Navigation: Click-to-navigate functionality Command Execution: Run preview commands for state management Multi-App Support: Connect multiple app instances simultaneously Auto-Discovery: Automatic detection of components and previews Auto-Generation Features Intelligent Component Discovery HotPreview automatically discovers and creates previews for UI components without any configuration. Auto-generated for: Pages: Any class inheriting from platform page base types Controls: Any class inheriting from platform view base types Dependency Injection: Components with constructor parameters resolved via DI Customizable discovery: Configure platform-specific base types Control auto-generation per component Organize components into categories Zero-Configuration Setup Get started immediately without complex configuration: Install DevTools globally Add package reference to your app Build and run in Debug mode Advanced Features Custom Preview Commands Define commands that can be executed from DevTools to manipulate app state or perform testing actions. #if PREVIEWS [PreviewCommand(\"Reset User Session\")] public static void ResetSession() { UserService.ClearSession(); App.Current.MainPage = new LoginPage(); } [PreviewCommand(\"Load Test Data\")] public static async Task LoadTestData() { await DataService.LoadSampleDataAsync(); } #endif Hierarchical Organization Use path-like naming to organize components and previews into logical hierarchies. #if PREVIEWS [Preview(\"Authentication/Login Form\")] public static LoginView LoginForm() => new(); [Preview(\"Authentication/Registration Form\")] public static RegisterView RegisterForm() => new(); [Preview(\"Shop/Product Card/Featured\")] public static ProductCard FeaturedProduct() => new(PreviewData.GetFeaturedProduct()); #endif Conditional Compilation Ensure preview code is completely excluded from release builds. #if PREVIEWS // All preview code goes here // Excluded from Release builds automatically #endif Upcoming Features 🤖 AI-Driven Development (Coming Soon) Built-in MCP (Model Context Protocol) server for AI-assisted UI development workflows. Planned capabilities: AI agents can generate and execute previews Automatic screenshot comparison and feedback Visual regression testing with AI analysis Intelligent component generation based on visual requirements 📊 Visual Testing Utils Advanced utilities for visual regression testing and comparison. Current capabilities: Image snapshot generation Visual difference detection Automated screenshot comparison Integration with testing frameworks 🔧 Enhanced DevTools Continuous improvements to the DevTools experience: Performance profiling Component dependency visualization Advanced filtering and search Custom themes and layouts Integration Features MSBuild Integration Seamless integration with your build process: Automatic DevTools launch during Debug builds App settings generation Conditional compilation symbol management Platform Support Current support: .NET MAUI (Windows, Android, iOS, macOS) Planned support: WPF WinUI 3 Uno Platform Avalonia UI Development Workflow IDE Integration: Works with Visual Studio Compatible with VS Code Command-line friendly Testing Integration: xUnit compatibility Visual regression testing Snapshot testing utilities Performance Features Lazy Loading Components and previews are loaded on-demand to maintain performance with large applications. Efficient Discovery Roslyn-based analysis for fast component discovery without runtime overhead. Memory Management Smart memory management to handle multiple app instances and component states efficiently."}, "docs/getting-started.html": {"href": "docs/getting-started.html", "title": "Getting Started | Hot Preview Documentation", "summary": "Getting Started This guide will help you set up HotPreview in your .NET application and create your first previews. Installation Prerequisites .NET 9.0.300 SDK or later (see global.json in the repository root) A supported .NET UI platform (currently .NET MAUI, with WPF support coming soon) Step 1: Install HotPreview DevTools Install the global DevTools application: dotnet tool install -g HotPreview.DevTools Step 2: Add Package Reference Add the HotPreview package to your application project. We recommend only including it in Debug builds: <PackageReference Condition=\"$(Configuration) == 'Debug'\" Include=\"HotPreview.App.Maui\" Version=\"...\" /> Step 3: Build and Run Build your application in Debug mode and run it: dotnet build # Run your application When you build and run your app: The DevTools application launches automatically (if not already running) Your app connects to DevTools when it starts DevTools displays a tree of your UI components and previews Auto-Generated Previews HotPreview automatically creates previews for UI components that meet these criteria: Pages Derives (directly or indirectly) from Microsoft.Maui.Controls.Page Has a parameterless constructor OR constructor parameters that can be resolved via dependency injection Controls Derives from Microsoft.Maui.Controls.View (but is not a page) Has a parameterless constructor OR constructor parameters that can be resolved via dependency injection Creating Custom Previews Custom previews give you full control over how your components are displayed. They allow you to: Support components with complex constructor requirements Provide realistic sample data Create multiple previews for different states Configure global app state for specific scenarios Basic Preview Add a static method with the [Preview] attribute to your UI component class: #if PREVIEWS [Preview] public static ConfirmAddressView Preview() => new(PreviewData.GetPreviewProducts(1), new DeliveryTypeModel(), new AddressModel() { StreetOne = \"21, Alex Davidson Avenue\", StreetTwo = \"Opposite Omegatron, Vicent Quarters\", City = \"Victoria Island\", State = \"Lagos State\" }); #endif Multiple Previews Create multiple previews to show different states: #if PREVIEWS [Preview(\"Empty State\")] public static CardView NoCards() => new(PreviewData.GetPreviewCards(0)); [Preview(\"Single Card\")] public static CardView SingleCard() => new(PreviewData.GetPreviewCards(1)); [Preview(\"Multiple Cards\")] public static CardView SixCards() => new(PreviewData.GetPreviewCards(6)); #endif Preview Guidelines Use conditional compilation: Wrap preview code in #if PREVIEWS to exclude it from release builds Provide meaningful names: Use descriptive names for multiple previews Use sample data: Create realistic test data to showcase your components Location flexibility: Preview methods can be in any class, but by convention are placed in the component class Navigation and Testing Once your app is running with DevTools: Browse Components: Use the DevTools tree view to explore your UI components Navigate Instantly: Click any component or preview to navigate directly to it in your app Test States: Use multiple previews to quickly test different data states Cross-Platform: Run your app on different platforms and compare side-by-side Best Practices Conditional Builds: Always use #if PREVIEWS for preview code Sample Data: Create dedicated preview data classes for consistent testing Descriptive Names: Use clear, descriptive names for multiple previews Edge Cases: Create previews for empty states, error conditions, and loading states Component Isolation: Ensure previews work independently of app navigation state Next Steps Learn about all available attributes Explore advanced features Check out the API reference"}, "docs/samples.html": {"href": "docs/samples.html", "title": "Sample Applications | Hot Preview Documentation", "summary": "Sample Applications HotPreview includes several sample applications that demonstrate different features and usage patterns. These samples serve as both learning resources and testing environments for the framework. Available Samples E-commerce MAUI Application Location: samples/maui/EcommerceMAUI/ Description: A complete e-commerce mobile application showcasing complex UI components and previews. Features Demonstrated Product browsing and search Shopping cart functionality User authentication flows Payment and checkout processes Order tracking Profile management Preview Examples The sample includes extensive preview definitions for various component states: Card Component Previews: #if PREVIEWS [Preview(\"No Cards\")] public static CardView NoCards() => new(PreviewData.GetPreviewCards(0)); [Preview(\"Single Card\")] public static CardView SingleCard() => new(PreviewData.GetPreviewCards(1)); [Preview(\"Two Cards\")] public static CardView TwoCards() => new(PreviewData.GetPreviewCards(2)); [Preview(\"Six Cards\")] public static CardView SixCards() => new(PreviewData.GetPreviewCards(6)); #endif Cart Component States: #if PREVIEWS [Preview(\"Empty Cart\")] public static CartView EmptyCart() => new(PreviewData.GetEmptyCart()); [Preview(\"Single Item Cart\")] public static CartView SingleItemCart() => new(PreviewData.GetCart(1)); [Preview(\"Medium Cart\")] public static CartView MediumCart() => new(PreviewData.GetCart(3)); [Preview(\"Large Cart\")] public static CartView LargeCart() => new(PreviewData.GetCart(10)); #endif Visual Regression Testing The sample includes automated visual testing with snapshot comparisons: Generated Snapshots: EcommerceMAUI.Views.AddNewCardView.png EcommerceMAUI.Views.CartView-SingleItemCart.png EcommerceMAUI.Views.ProductDetailsView.png EcommerceMAUI.Views.HomePageView.png Running the Sample cd samples/maui/EcommerceMAUI dotnet build dotnet run --project EcommerceMAUI.csproj Default Template with Content Location: samples/maui/DefaultTemplateWithContent/ Description: A project management application demonstrating data-driven UI components. Features Demonstrated Project and task management Category-based organization Data visualization with charts CRUD operations Tag-based filtering Components Included ProjectCardView - Project summary cards TaskView - Individual task components CategoryChart - Data visualization TagView - Tag display components AddButton - Action components Preview Data Management The sample shows how to create comprehensive preview data: public static class PreviewData { public static List<Project> GetPreviewProjects(int count) { return MockData.Projects.Take(count).ToList(); } public static List<ProjectTask> GetPreviewTasks(int count) { return MockData.Tasks.Take(count).ToList(); } public static CategoryChartData GetPreviewChartData() { return new CategoryChartData { Categories = MockData.Categories.Take(5).ToList(), Data = GenerateRandomData(5) }; } } Common Patterns Preview Data Organization Both samples demonstrate effective preview data management: Centralized Data Service: public static class PreviewData { // Static data for consistent previews public static readonly Product FeaturedProduct = new() { Name = \"Premium Headphones\", Price = 299.99m, Description = \"High-quality wireless headphones\", IsAvailable = true }; // Dynamic data generation public static List<Product> GetProducts(int count, bool includeFeatured = false) { var products = MockData.Products.Take(count).ToList(); if (includeFeatured) products.Insert(0, FeaturedProduct); return products; } } State Variation Patterns Loading and Error States: #if PREVIEWS [Preview(\"Normal State\")] public static ProductView Normal() => new(PreviewData.FeaturedProduct); [Preview(\"Loading State\")] public static ProductView Loading() => new(isLoading: true); [Preview(\"Error State\")] public static ProductView Error() => new(hasError: true, errorMessage: \"Failed to load product\"); [Preview(\"Empty State\")] public static ProductView Empty() => new(isEmpty: true); #endif Hierarchical Organization Grouped Previews: #if PREVIEWS // Authentication flows [Preview(\"Auth/Login Form\")] public static LoginView LoginForm() => new(); [Preview(\"Auth/Register Form\")] public static RegisterView RegisterForm() => new(); [Preview(\"Auth/Forgot Password\")] public static ForgotPasswordView ForgotPassword() => new(); // Shopping features [Preview(\"Shop/Product List\")] public static ProductListView ProductList() => new(PreviewData.GetProducts(10)); [Preview(\"Shop/Product Details\")] public static ProductDetailsView ProductDetails() => new(PreviewData.FeaturedProduct); #endif Testing Integration Visual Regression Testing Both samples include comprehensive visual testing: Test Structure: samples/maui/EcommerceMAUI/ ├── snapshots/ # Reference images ├── test-results/ # Test output └── VisualTests.cs # Test definitions Example Test: [Fact] public async Task CartView_SingleItem_MatchesSnapshot() { var component = CartView.SingleItemCart(); var screenshot = await CaptureScreenshot(component); await VisualRegressionTester.AssertMatchesSnapshot( screenshot, \"EcommerceMAUI.Views.CartView-SingleItem.png\" ); } Unit Testing Integration with xUnit for component testing: [Fact] public void ProductCard_WithValidProduct_DisplaysCorrectly() { // Arrange var product = PreviewData.FeaturedProduct; // Act var card = new ProductCard(product); // Assert Assert.Equal(product.Name, card.ProductName.Text); Assert.Equal(product.Price.ToString(\"C\"), card.Price.Text); } Development Workflow Iterative Development The samples demonstrate an efficient development workflow: Create Component: Build basic UI component Add Preview: Create initial [Preview] method Test States: Add previews for different states Refine UI: Use DevTools to iterate quickly Add Tests: Create visual regression tests Document: Update preview names and organization Best Practices from Samples Naming Conventions: Use descriptive, hierarchical names Group related previews with \"/\" delimiters Include state information in names Data Management: Centralize preview data in dedicated classes Use realistic data that represents actual usage Include edge cases and boundary conditions Component Design: Design components to be preview-friendly Support parameterless constructors where possible Use dependency injection for complex dependencies Running All Samples Build All Samples dotnet build samples/HotPreview-Samples.slnf Run Individual Samples # E-commerce sample cd samples/maui/EcommerceMAUI dotnet run # Project management sample cd samples/maui/DefaultTemplateWithContent dotnet run Visual Testing # Run visual regression tests dotnet test samples/maui/EcommerceMAUI --logger \"console;verbosity=detailed\" Learning Resources Component Examples Study the sample components to learn: Effective preview design patterns State management for previews Data binding in preview contexts Platform-specific adaptations Preview Strategies The samples showcase various preview strategies: Minimal previews for simple components Comprehensive state coverage for complex components Data-driven previews for components with varying content Interactive previews with command support Testing Approaches Learn from the sample testing strategies: Visual regression testing for UI consistency Unit testing for component logic Integration testing for component interactions Cross-platform testing for platform consistency"}, "index.html": {"href": "index.html", "title": "Hot Preview Documentation | Hot Preview Documentation", "summary": "Hot Preview Documentation Hot Preview lets you easily work on pages and controls in your app in isolation, making UI development and testing faster and more efficient for both humans and AI agents. Previews are similar to stories in Storybook for JavaScript and Previews in SwiftUI/Xcode and Jetpack Compose/Android Studio — but for .NET UI. Quick Start Get started with HotPreview in minutes: Install Hot Preview DevTools: dotnet tool install -g HotPreview.DevTools Add package reference to your app: <PackageReference Condition=\"$(Configuration) == 'Debug'\" Include=\"HotPreview.App.Maui\" Version=\"...\" /> Build and run your app in Debug mode Features 🚀 Streamlined Navigation - Jump directly to specific UI pages without complex navigation 🔄 Multi-state Testing - Visualize components with different data inputs and states 📱 Cross-platform Preview - View UI on multiple platforms simultaneously 🤖 AI Integration - Built-in MCP server for agentic AI workflows (Coming Soon) Get Started → | API Reference →"}}